<?php

namespace Src\Domain\Api\Requests\Auth;

use Src\Domain\Api\Models\User\UserForm;
use Src\Domain\Api\Requests\FormRequest;
use Illuminate\Validation\Rule;
use Src\Enums\CertificateLevel;
use Src\Enums\Gender;
use Src\Enums\PeriodType;
use Src\Enums\UserBankType;

/**
 * Class LoginRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class BaseRequestUser extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'max:100',
            ],
            'email' => [
                'required',
                'string',
                'max:100',
                'unique:t_users,email,'.$this->user()?->id,
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'max:100',
            ],
            'avatar_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'health_certificate_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'name_kana' => [
                'nullable',
                'max:100',
            ],
            'name_kanji' => [
                'nullable',
                'max:100',
            ],
            'phone_number' => [
                'required',
                'max:15',
            ],
            'gender' => [
                'required',
                Rule::in(Gender::asArray())
            ],
            'birthday' => [
                'required',
            ],
            'nationality' => [
                'required',
            ],
            'has_certificate' => [
                'required',
                'boolean',
            ],
            'japanese_level' => [
                'required',
                Rule::in(CertificateLevel::asArray())
            ],
            'arrival_date' => [
                'required',
            ],
            'zip_code' => [
                'required',
                'max:8'
            ],
            'prefecture' => [
                'required',
                'max:100'
            ],
            'street_address' => [
                'required',
                'max:255'
            ],
            'town_address' => [
                'nullable',
                'max:255'
            ],
            'train_station_name' => [
                'required',
                'max:255'
            ],
            'emergency_name' => [
                'nullable',
                'max:100'
            ],
            'emergency_relation' => [
                'nullable',
            ],
            'emergency_phone_number' => [
                'nullable',
                'max:15'
            ],
            'bank_type' => [
                'required',
                Rule::in(UserBankType::asArray())
            ],
            'atm_image_id' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'bank_code' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'branch_code' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'deposit_type' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'account_name' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'account_number' => [
                'required_if:bank_type,' . UserBankType::BANK,
            ],
            'front_card_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'back_card_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'period_type' => [
                'required',
                Rule::in(PeriodType::asArray())
            ],
            'school_name' => [
                'required_if:period_type,' . PeriodType::STUDENT,
            ],
            'front_identification_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'back_identification_id' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:5120',
            ],
            'period_of_stay' => [
                'required',
            ],
            'identification_expired_at' => [
                'required',
            ],
            'passport_image_id' => [
                'required',
            ],
            'passport_number' => [
                'required',
            ],
            'passport_expired_at' => [
                'required',
            ]
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [

        ];
    }

    /**
     * @return UserForm
     */
    public function validatedForm(): UserForm
    {
        return new UserForm($this->validated());
    }
}
