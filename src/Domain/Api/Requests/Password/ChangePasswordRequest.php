<?php

namespace Src\Domain\Api\Requests\Password;

use Src\Domain\Api\Models\Password\PasswordForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class ChangePasswordRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class ChangePasswordRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'current_password' => [
                'required',
                'string',
                'min:8',
                'max:100',
            ],
            'new_password' => [
                'required',
                'string',
                'min:8',
                'max:100',
                'different:current_password',
            ],
            'new_password_confirmation' => [
                'required',
                'same:new_password'
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'old_password' => 'old password',
            'new_password' => 'new password',
            'confirm_password' => 'confirm new password',
        ];
    }

    /**
     * Messages
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'new_password.different' => 'The new password must be different from the old password.',
            'confirm_password.same' => 'The new password confirmation does not match.',
        ];
    }

    /**
     * @return PasswordForm
     */
    public function validatedForm(): PasswordForm
    {
        return new PasswordForm($this->validated());
    }
}
