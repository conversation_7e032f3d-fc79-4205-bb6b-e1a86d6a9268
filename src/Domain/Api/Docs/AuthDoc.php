<?php

namespace Src\Domain\Api\Docs;

use OpenApi\Attributes as OA;

#[OA\Tag(
    name: 'Auth',
    description: 'Authentication endpoints'
)]
class AuthDoc
{
    #[OA\Post(
        path: '/login',
        summary: 'Login User',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['email', 'password'],
                    properties: [
                        new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                        new OA\Property(property: 'password', type: 'string', format: 'password', example: '12345678')
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Login successful',
                content: new OA\JsonContent(ref: '#/components/schemas/LoginResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function login() {}

    #[OA\Post(
        path: '/logout',
        summary: 'Logout User',
        security: [['passport' => []]],
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Logout successful',
                content: new OA\JsonContent(ref: '#/components/schemas/SuccessResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function logout() {}

    #[OA\Post(
        path: '/sign-up',
        summary: 'Sign Up User',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: [
                        'name', 'email', 'password', 'avatar_id', 'health_certificate_id', 'phone_number', 'gender', 'birthday',
                        'nationality', 'has_certificate', 'japanese_lever', 'arrival_date',
                        'zip_code', 'prefecture', 'street_address', 'train_station_name',
                        'bank_type', 'front_card_id', 'back_card_id', 'period_type', 'japanese_level',
                        'period_of_stay', 'identification_expired_at', 'passport_image_id', 'passport_number', 'passport_expired_at',
                        'front_identification_id', 'back_identification_id'
                    ],
                    properties: [
                        new OA\Property(property: 'name', type: 'string', maxLength: 100),
                        new OA\Property(property: 'email', type: 'string', format: 'email', maxLength: 100),
                        new OA\Property(property: 'password', type: 'string', format: 'password', maxLength: 100, minLength: 8),
                        new OA\Property(property: 'avatar_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'health_certificate_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'name_kana', type: 'string', maxLength: 100),
                        new OA\Property(property: 'name_kanji', type: 'string', maxLength: 100),
                        new OA\Property(property: 'phone_number', type: 'string', maxLength: 15),
                        new OA\Property(property: 'gender', type: 'string', enum: ['MALE', 'FEMALE']),
                        new OA\Property(property: 'birthday', type: 'string', format: 'date'),
                        new OA\Property(property: 'nationality', type: 'string'),
                        new OA\Property(property: 'has_certificate', type: 'boolean'),
                        new OA\Property(property: 'japanese_level', type: 'string', enum: ['N1', 'N2', 'N3', 'N4', 'N5', 'SAME_N1', 'SAME_N2', 'SAME_N3', 'SAME_N4', 'SAME_N5']),
                        new OA\Property(property: 'arrival_date', type: 'string', format: 'date'),
                        new OA\Property(property: 'zip_code', type: 'string', maxLength: 8),
                        new OA\Property(property: 'prefecture', type: 'string', maxLength: 100),
                        new OA\Property(property: 'street_address', type: 'string', maxLength: 255),
                        new OA\Property(property: 'town_address', type: 'string', maxLength: 255),
                        new OA\Property(property: 'train_station_name', type: 'string', maxLength: 255),
                        new OA\Property(property: 'emergency_name', type: 'string', maxLength: 100, nullable: true),
                        new OA\Property(property: 'emergency_relation', type: 'string', nullable: true),
                        new OA\Property(property: 'emergency_phone_number', type: 'string', maxLength: 15, nullable: true),
                        new OA\Property(property: 'bank_type', type: 'string', enum: ['BANK', 'CASH']),
                        new OA\Property(property: 'atm_image_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'bank_code', type: 'string', nullable: true),
                        new OA\Property(property: 'branch_code', type: 'string', nullable: true),
                        new OA\Property(property: 'deposit_type', type: 'string', nullable: true),
                        new OA\Property(property: 'account_name', type: 'string', nullable: true),
                        new OA\Property(property: 'account_number', type: 'string', nullable: true),
                        new OA\Property(property: 'front_card_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'back_card_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'period_type', type: 'string', enum: ['STUDENT', 'OTHER']),
                        new OA\Property(property: 'school_name', type: 'string', nullable: true),
                        new OA\Property(property: 'front_identification_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'back_identification_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'period_of_stay', type: 'string', format: 'date'),
                        new OA\Property(property: 'identification_expired_at', type: 'string', format: 'date'),
                        new OA\Property(property: 'passport_image_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary'),
                        new OA\Property(property: 'passport_number', type: 'string', nullable: true),
                        new OA\Property(property: 'passport_expired_at', type: 'datetime', nullable: true)
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Sign up successful',
                content: new OA\JsonContent(ref: '#/components/schemas/UserSuccessResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Validation error',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
        ]
    )]
    public function signUp() {}

    #[OA\Post(
        path: '/forgot-password',
        summary: 'Forgot Password',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['email', 'birthday'],
                    properties: [
                        new OA\Property(property: 'email', type: 'string', format: 'email'),
                        new OA\Property(property: 'birthday', type: 'string', maxLength: 8)

                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Reset password email sent successfully',
                content: new OA\JsonContent(ref: '#/components/schemas/SuccessResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Validation error',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'User not found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function forgotPassword() {}

    #[OA\Post(
        path: '/reset-password',
        summary: 'Reset Password',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['token', 'password', 'password_confirmation'],
                    properties: [
                        new OA\Property(property: 'token', type: 'string'),
                        new OA\Property(property: 'password', type: 'string', format: 'password', minLength: 8),
                        new OA\Property(property: 'password_confirmation', type: 'string', format: 'password', minLength: 8)
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Password reset successfully',
                content: new OA\JsonContent(ref: '#/components/schemas/SuccessResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Validation error',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
        ]
    )]
    public function resetPassword() {}

    #[OA\Post(
        path: '/change-password',
        summary: 'Change Password',
        security: [['passport' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['current_password', 'new_password', 'new_password_confirmation'],
                    properties: [
                        new OA\Property(property: 'current_password', type: 'string', format: 'password', minLength: 8),
                        new OA\Property(property: 'new_password', type: 'string', format: 'password', minLength: 8),
                        new OA\Property(property: 'new_password_confirmation', type: 'string', format: 'password', minLength: 8)
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Password changed successfully',
                content: new OA\JsonContent(ref: '#/components/schemas/SuccessResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Validation error',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
        ]
    )]
    public function changePassword() {}

    #[OA\Get(
        path: '/get-profile',
        summary: 'Get profile information',
        security: [['passport' => []]],
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/ProfileResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function getProfile() {}

    #[OA\Post(
        path: '/verify-email',
        summary: 'Verify email account',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['token'],
                    properties: [
                        new OA\Property(property: 'token', type: 'string'),
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Verify email successful',
                content: new OA\JsonContent(ref: '#/components/schemas/VerifyEmailResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Verify email failed',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function verifyEmail() {}

    #[OA\Post(
        path: '/update-profile',
        summary: 'Update Profile',
        security: [['passport' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: [
                        'name', 'email', 'password', 'phone_number', 'gender', 'birthday',
                        'nationality', 'has_certificate', 'japanese_lever', 'arrival_date',
                        'zip_code', 'prefecture', 'street_address', 'train_station_name',
                        'bank_type', 'period_type', 'japanese_level',
                        'period_of_stay', 'identification_expired_at', 'passport_image_id', 'passport_number', 'passport_expired_at',
                    ],
                    properties: [
                        new OA\Property(property: 'name', type: 'string', maxLength: 100),
                        new OA\Property(property: 'email', type: 'string', format: 'email', maxLength: 100),
                        new OA\Property(property: 'password', type: 'string', format: 'password', maxLength: 100, minLength: 8),
                        new OA\Property(property: 'avatar_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'health_certificate_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'name_kana', type: 'string', maxLength: 100),
                        new OA\Property(property: 'name_kanji', type: 'string', maxLength: 100),
                        new OA\Property(property: 'phone_number', type: 'string', maxLength: 15),
                        new OA\Property(property: 'gender', type: 'string', enum: ['MALE', 'FEMALE']),
                        new OA\Property(property: 'birthday', type: 'string', format: 'date'),
                        new OA\Property(property: 'nationality', type: 'string'),
                        new OA\Property(property: 'has_certificate', type: 'boolean'),
                        new OA\Property(property: 'japanese_level', type: 'string', enum: ['N1', 'N2', 'N3', 'N4', 'N5', 'SAME_N1', 'SAME_N2', 'SAME_N3', 'SAME_N4', 'SAME_N5']),
                        new OA\Property(property: 'arrival_date', type: 'string', format: 'date'),
                        new OA\Property(property: 'zip_code', type: 'string', maxLength: 8),
                        new OA\Property(property: 'prefecture', type: 'string', maxLength: 100),
                        new OA\Property(property: 'street_address', type: 'string', maxLength: 255),
                        new OA\Property(property: 'town_address', type: 'string', maxLength: 255),
                        new OA\Property(property: 'train_station_name', type: 'string', maxLength: 255),
                        new OA\Property(property: 'emergency_name', type: 'string', maxLength: 100, nullable: true),
                        new OA\Property(property: 'emergency_relation', type: 'string', nullable: true),
                        new OA\Property(property: 'emergency_phone_number', type: 'string', maxLength: 15, nullable: true),
                        new OA\Property(property: 'bank_type', type: 'string', enum: ['BANK', 'CASH']),
                        new OA\Property(property: 'atm_image_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'bank_code', type: 'string', nullable: true),
                        new OA\Property(property: 'branch_code', type: 'string', nullable: true),
                        new OA\Property(property: 'deposit_type', type: 'string', nullable: true),
                        new OA\Property(property: 'account_name', type: 'string', nullable: true),
                        new OA\Property(property: 'account_number', type: 'string', nullable: true),
                        new OA\Property(property: 'front_card_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'back_card_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'period_type', type: 'string', enum: ['STUDENT', 'OTHER']),
                        new OA\Property(property: 'school_name', type: 'string', nullable: true),
                        new OA\Property(property: 'front_identification_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'back_identification_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'period_of_stay', type: 'string', format: 'date'),
                        new OA\Property(property: 'identification_expired_at', type: 'string', format: 'date'),
                        new OA\Property(property: 'passport_image_id', description: 'Image file (jpeg,png,jpg) max 5MB', type: 'string', format: 'binary', nullable: true),
                        new OA\Property(property: 'passport_number', type: 'string', nullable: true),
                        new OA\Property(property: 'passport_expired_at', type: 'datetime', nullable: true)
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Sign up successful',
                content: new OA\JsonContent(ref: '#/components/schemas/UserSuccessResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Validation error',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/AuthorizedResponse')
            )
        ]
    )]
    public function updateProfile() {}

    #[OA\Post(
        path: '/check-exist-email',
        summary: 'Check exist email',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: [
                        'email',
                    ],
                    properties: [
                        new OA\Property(property: 'email', type: 'string', format: 'email', maxLength: 100),
                    ]
                )
            )
        ),
        tags: ['Auth'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Check exist email successful',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'result_code', type: 'number', example: 200),
                        new OA\Property(property: 'result_detail', type: 'boolean')
                    ],
                    type: 'object'
                )
            )
        ]
    )]
    public function checkExistEmail() {}
}
