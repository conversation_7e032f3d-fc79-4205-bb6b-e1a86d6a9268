<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;
#[OA\Schema(
    schema: 'ProfileResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(property: 'code', type: 'string'),
                new OA\Property(property: 'email', type: 'string'),
                new OA\Property(property: 'name', type: 'string'),
                new OA\Property(property: 'nameKana', type: 'string'),
                new OA\Property(property: 'nameKanji', type: 'string'),
                new OA\Property(property: 'avatarUrl', type: 'string'),
                new OA\Property(property: 'healthCertificateUrl', type: 'string'),
                new OA\Property(property: 'userStatus', type: 'string'),
                new OA\Property(property: 'phoneNumber', type: 'string'),
                new OA\Property(property: 'gender', type: 'string'),
                new OA\Property(property: 'birthday', type: 'date'),
                new OA\Property(property: 'nationality', type: 'string'),
                new OA\Property(property: 'hasCertificate', type: 'boolean'),
                new OA\Property(property: 'japaneseLevel', type: 'string'),
                new OA\Property(property: 'arrivalDate', type: 'date'),
                new OA\Property(property: 'zipCode', type: 'string'),
                new OA\Property(property: 'prefecture', type: 'string'),
                new OA\Property(property: 'streetAddress', type: 'string'),
                new OA\Property(property: 'townAddress', type: 'string'),
                new OA\Property(property: 'trainStationName', type: 'string'),
                new OA\Property(property: 'emergencyName', type: 'string'),
                new OA\Property(property: 'emergencyRelation', type: 'string'),
                new OA\Property(property: 'emergencyPhoneNumber', type: 'string'),
                new OA\Property(property: 'bankType', type: 'string'),
                new OA\Property(property: 'point', type: 'number'),
                new OA\Property(property: 'passportImageUrl', type: 'string'),
                new OA\Property(property: 'passportNumber', type: 'string'),
                new OA\Property(property: 'passportExpiredAt', type: 'string'),
                new OA\Property(property: 'numberUnreadNotification', type: 'number'),
                new OA\Property(property: 'verificationAt', type: 'datetime'),
                new OA\Property(property: 'identificationAt', type: 'datetime'),
                new OA\Property(property: 'isDisable', type: 'boolean'),

                new OA\Property(property: 'userBank', properties: [
                    new OA\Property(property: 'atmImageUrl', type: 'string'),
                    new OA\Property(property: 'bankCode', type: 'string'),
                    new OA\Property(property: 'branchCode', type: 'string'),
                    new OA\Property(property: 'accountName', type: 'string'),
                    new OA\Property(property: 'accountNumber', type: 'string'),
                    new OA\Property(property: 'depositType', type: 'string'),
                ]),
                new OA\Property(property: 'userResidenceCard', properties: [
                    new OA\Property(property: 'frontCardUrl', type: 'string'),
                    new OA\Property(property: 'backCardUrl', type: 'string'),
                    new OA\Property(property: 'periodType', type: 'string'),
                    new OA\Property(property: 'schoolName', type: 'string'),
                    new OA\Property(property: 'frontIdentificationUrl', type: 'string'),
                    new OA\Property(property: 'backIdentificationUrl', type: 'string'),
                    new OA\Property(property: 'identificationExpiredAt', type: 'datetime'),
                    new OA\Property(property: 'periodOfStay', type: 'datetime'),
                ]),
            ],
            type: 'object'
        ),
    ]
)]

#[OA\Schema(
    schema: 'BaseInfoResponse',
    properties: [
        new OA\Property(property: 'id', type: 'number'),
        new OA\Property(property: 'name', type: 'string'),
        new OA\Property(property: 'email', type: 'string'),
        new OA\Property(property: 'avatarUrl', type: 'string'),
        new OA\Property(property: 'userStatus', type: 'string'),
        new OA\Property(property: 'nameKana', type: 'string'),
        new OA\Property(property: 'nameKanji', type: 'string'),
        new OA\Property(property: 'phoneNumber', type: 'string'),
        new OA\Property(property: 'gender', type: 'string'),
        new OA\Property(property: 'birthday', type: 'date'),
        new OA\Property(property: 'national', type: 'string'),
        new OA\Property(property: 'japaneseLevel', type: 'string'),
        new OA\Property(property: 'zipcode', type: 'string'),
        new OA\Property(property: 'prefecture', type: 'string'),
        new OA\Property(property: 'streetAddress', type: 'string'),
        new OA\Property(property: 'townAddress', type: 'string'),
        new OA\Property(property: 'trainStationName', type: 'string'),
        new OA\Property(property: 'numberUnreadNotification', type: 'number'),
    ]
)]

#[OA\Schema(
    schema: 'UserSuccessResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'string',
        )
    ]
)]

class UserSchema {}
