<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Eloquent\UserResetPassword;
use App\Exceptions\APIRuntimeException;
use App\Mail\ForgotPasswordMail;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Src\Domain\Api\Models\Password\ForgotPasswordForm;
use Src\Domain\Api\Models\Password\PasswordForm;
use Src\Domain\Api\Models\Password\ResetPasswordDetail;
use Src\Domain\Api\Models\Password\ResetPasswordForm;
use DB;
use Src\Enums\ApprovalStatus;
use Throwable;

class PasswordService
{
    /**
     * Change password
     *
     * @param PasswordForm $form
     * @return array|false|mixed
     */
    public function changePasswordServices(PasswordForm $form): mixed
    {
        $result = false;

        /** @var User $user */
        $authenticatedUser = auth('api')->user();
        $user = $this->userQuery()->where('id', $authenticatedUser->id)->firstOrFail();

        if (!Hash::check($form->getCurrentPassword(), $user->password)) {
            throw new APIRuntimeException('api_errors.user.old_password_not_match');
        }
        try {
            $result = DB::transaction(function () use ($form, $user) {
                $user->updateOrThrow($form->updateAttributes());
                logger_info('Password updated successfully.', ['user_id' => $user->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update password', ['form_data' => $form->toArray()], $e);
        }

        return $result;
    }

    /**
     * Change password
     *
     * @param ForgotPasswordForm $form
     * @return array|false|mixed
     */
    public function forgotPasswordServices(ForgotPasswordForm $form): mixed
    {
        $result = false;

        $email = $form->getEmail();
        $birthday = $form->getBirthday();

        // Find user
        /** @var User $user */
        $user = $this->userQuery()->where('email', $email)->first();
        if (!$user) {
            throw new APIRuntimeException('api_errors.user.not_found');
        }

        if($user->user_status != ApprovalStatus::APPROVED ){
            throw new APIRuntimeException('api_errors.user.requires_admin_approval');
        }

        // Convert input birthday to Y-m-d format
        $inputBirthday = \Carbon\Carbon::createFromFormat('Ymd', $birthday)->format('Y-m-d');
        $userBirthday = $user->birthday instanceof \Carbon\Carbon
            ? $user->birthday->format('Y-m-d')
            : (new \Carbon\Carbon($user->birthday))->format('Y-m-d');

        // Check if birthday matches
        if ($userBirthday !== $inputBirthday) {
            throw new APIRuntimeException('api_errors.user.birthday_not_match');
        }

        /** @var UserResetPassword $reset_exist */
        $reset_exist = $this->userResetPasswordQuery()->where('email', $email)->first();
        if ($reset_exist && $reset_exist->expired_at >= now()) {
            throw new APIRuntimeException('api_errors.user.reset_password_exist');
        }

        try {
            $result = DB::transaction(function () use ($form, $email) {
                $token = Str::random(64);

                /** @var UserResetPassword $password_reset */
                $password_reset = $this->userResetPasswordQuery()->createOrThrow($form->createAttributes($token));

                $resetLink =  [
                    'token' => $token,
                    'email' => $email
                ];

                // Send email
                Mail::to($email)->send(new ForgotPasswordMail($email, $resetLink));

                logger_info('Password reset token generated successfully', [
                    'email' => $email,
                    'token' => $token
                ]);

                return (new ResetPasswordDetail($password_reset))->toDetailApiResponse();
            });
        } catch (Throwable $e) {
            logger_error('Fail to reset password', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Reset password
     *
     * @param ResetPasswordForm $form
     * @return array|false|mixed
     */
    public function resetPasswordServices(ResetPasswordForm $form): mixed
    {
        $result = false;

        $token = $form->getToken();
        /** @var UserResetPassword $userResetPassword */
        $userResetPassword = $this->userResetPasswordQuery()->where('token', $token)->first();

        if (!$userResetPassword) {
            throw new APIRuntimeException('api_errors.user.token_not_found');
        }

        if (Carbon::now()->greaterThan(Carbon::parse($userResetPassword->expired_at))) {
            throw new APIRuntimeException('api_errors.user.token_expired');
        }

        /** @var User $user */
        $user = $this->userQuery()->where('email', $userResetPassword->email)->firstOrFail();

        try {
            $result = DB::transaction(function () use ($form, $user) {
                $user->updateOrThrow($form->updateAttributes());
                logger_info('Password reset successfully for user', ['email' => $user->email]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to reset user password', ['form_data' => $form->toArray()], $e);
        }

        return $result;
    }

    private function userResetPasswordQuery()
    {
        return UserResetPassword::queryModel();
    }

    /**
     * @return Builder|User
     */
    private function userQuery(): Builder|User
    {
        return User::query()->getModel();
    }
}
