<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\User\UserSearchForm;
use Src\Domain\Admin\Requests\User\CreateRequest;
use Src\Domain\Admin\Requests\User\EditUserBaseRequest;
use Src\Domain\Admin\Requests\User\EditUserBankRequest;
use Src\Domain\Admin\Services\UserService;

/**
 * Class UserController
 * @package Src\Domain\Admin\Controllers
 */
class UserController extends Controller
{
    /**
     * @var UserService
     */
    protected UserService $service;

    /**
     * UserController constructor.
     *
     * @param UserService $service
     */
    public function __construct(UserService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the users.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $searchForm = new UserSearchForm($request->all());
        $users = $this->service->fetchAll($searchForm);

        return Inertia::render('User/IndexPage', [
            'users' => $users
        ]);
    }

    /**
     * Show the form for creating a new user.
     *
     * @return Response
     */
    public function create(): Response
    {
        return Inertia::render('User/CreatePage');
    }

    /**
     * Store a newly created user.
     *
     * @param CreateRequest $request
     * @return RedirectResponse
     */
    public function store(CreateRequest $request): RedirectResponse
    {
        $result = $this->service->store($request->validatedForm());

        if ($result) {
            return to_route('admin.user.index')
                ->with('success', __('flash.store.succeeded'));
        }

        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Display the specified user.
     *
     * @param int $userId
     * @return Response
     */
    public function show(int $userId): Response
    {
        $user = $this->service->findOrFail($userId);

        return Inertia::render('User/DetailPage', ['user' => $user]);
    }

    /**
     * Remove the specified user.
     *
     * @param int $userId
     * @return RedirectResponse
     */
    public function delete(int $userId): RedirectResponse
    {
        $result = $this->service->delete($userId);

        if ($result) {
            return to_route('admin.user.index')
                ->with('success', __('flash.delete.succeeded'));
        }

        return back()->with('error', __('flash.delete.failed'));
    }

    /**
     * Update user base info
     *
     * @param int $userId
     * @param EditUserBaseRequest $request
     * @return RedirectResponse
     */
    public function updateBase(int $userId, EditUserBaseRequest $request): RedirectResponse
    {
        $result = $this->service->updateBase($userId, $request->validatedForm());

        if ($result) {
            return to_route('admin.user.show', $userId)
                ->with('success', __('flash.update.succeeded'));
        }

        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Update user bank info
     *
     * @param int $userId
     * @param EditUserBankRequest $request
     * @return RedirectResponse
     */
    public function updateBank(int $userId, EditUserBankRequest $request): RedirectResponse
    {
        $result = $this->service->updateBank($userId, $request->validatedForm());

        if ($result) {
            return to_route('admin.user.show', $userId)
                ->with('success', __('flash.update.succeeded'));
        }

        return back()->with('error', __('flash.update.failed'));
    }
}
