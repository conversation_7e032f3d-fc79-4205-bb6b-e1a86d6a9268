<?php

namespace Src\Domain\Admin\Controllers;

use App\Eloquent\Account;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Src\Domain\Admin\Requests\Auth\LoginRequest;
use Src\Enums\ApprovalStatus;

/**
 * Class LoginController
 * @package Src\Domain\Admin\Controller
 */
class LoginController extends Controller
{
    /**
     * @return \Inertia\Response
     */
    public function show()
    {
        return Inertia::render('Auth/LoginPage');
    }

    /**
     * Login
     *
     * @param LoginRequest $request
     * @return RedirectResponse
     */
    public function login(LoginRequest $request)
    {
        $login_form = $request->validatedForm();

        $credentials = [
            'login_id' => $login_form->getLoginId(),
            'password' => $login_form->getPassword()
        ];
        $is_remember = (bool)$request->get('remember', false);

        /** @var Account $account */
        $account = Account::queryModel()->where('login_id', $login_form->getLoginId())->first();

        if ($account->remain_login === 0) {
            return back()->with('error', __('flash.login.no_remain'));
        }

        if (auth('admin')->attempt($credentials, $is_remember)) {
            auth()->guard('admin')->user();
            if ($account->remain_login > 0) {
                $account->update(['remain_login' => REMAIN_LOGIN]);
            }
            return to_route('admin.job-application.index');
        }
        Account::queryModel()->where('login_id', $login_form->getLoginId())->decrement('remain_login');
        return back()->with('error', __('flash.login.failed'));
    }

    public function logout()
    {
        Auth::guard('admin')->logout();
        return to_route('admin.auth.show');
    }
}
