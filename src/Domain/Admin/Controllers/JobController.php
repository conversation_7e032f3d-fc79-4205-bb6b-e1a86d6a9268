<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Job\JobSearchForm;
use Src\Domain\Admin\Requests\Job\CreateRequest;
use Src\Domain\Admin\Requests\Job\UpdateRequest;
use Src\Domain\Admin\Services\JobService;

/**
 * Class JobController
 * @package Src\Domain\Admin\Controllers
 */
class JobController extends Controller
{
    /**
     * @var JobService
     */
    protected JobService $service;

    /**
     * JobController constructor.
     *
     * @param JobService $service
     */
    public function __construct(JobService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the jobs.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $search_form = new JobSearchForm($request->all());
        $categories = $this->service->fetchCategories();
        $jobs = $this->service->fetchAll($search_form);
        return Inertia::render('Job/IndexPage', ['jobs' => $jobs, 'categories' => $categories]);
    }

    /**
     * Show the form for creating a new job.
     *
     * @return Response
     */
    public function create(): Response
    {
        $categories = $this->service->fetchCategories();
        return Inertia::render('Job/CreatePage', ['categories' => $categories]);
    }

    /**
     * Store a newly created job.
     *
     * @param CreateRequest $request
     * @return RedirectResponse
     */
    public function store(CreateRequest $request): RedirectResponse
    {
        if ($this->service->store($request->validatedForm())) {
            return to_route('admin.job.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Display the specified job.
     *
     * @param int $job_id
     * @return Response
     */
    public function show(int $job_id): Response
    {
        $job = $this->service->findOrFail($job_id);
        return Inertia::render('Job/DetailPage', ['job' => $job]);
    }

    /**
     * Show the form for editing the specified job.
     *
     * @param int $job_id
     * @return Response
     */
    public function edit(int $job_id): Response
    {
        $job = $this->service->findOrFail($job_id);
        $categories = $this->service->fetchCategories();
        return Inertia::render('Job/EditPage', ['job' => $job, 'categories' => $categories]);
    }

    /**
     * Update the specified job.
     *
     * @param int $job_id
     * @param UpdateRequest $request
     * @return RedirectResponse
     */
    public function update(int $job_id, UpdateRequest $request): RedirectResponse
    {
        $result = $this->service->update($job_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.job.show', $job_id)->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Remove the specified job.
     *
     * @param int $job_id
     * @return RedirectResponse
     */
    public function delete(int $job_id): RedirectResponse
    {
        if ($this->service->delete($job_id)) {
            return to_route('admin.job.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }

    /**
     * Duplicate the specified job.
     *
     * @param int $job_id
     * @return RedirectResponse
     */
    public function duplicate(int $job_id): RedirectResponse
    {
        if ($this->service->duplicate($job_id)) {
            return to_route('admin.job.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }
}
