<?php

namespace Src\Domain\Admin\Models\Account;

use App\Eloquent\Account;
use Src\Enums\AccountRole;
use Src\Enums\ApprovalStatus;

/**
 * Class AccountDetail
 * @package Src\Domain\Admin\Forms\Account
 */
class AccountDetail
{
    /**
     * @var Account
     */
    private $account;

    /**
     * Account constructor.
     * @param Account $account
     */
    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->account->id;
    }

    /**
     * @return string
     */
    public function getLoginId(): string
    {
        return $this->account->login_id;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->account->name;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->account->email;
    }

    /**
     * @return string
     */
    public function getRoleDiv(): string
    {
        return $this->account->role_div;
    }

    /**
     * @return string|null
     */
    public function getRoleDesc(): ?string
    {
        return AccountRole::getDescription($this->getRoleDiv());
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'loginId' => $this->getLoginId(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'roleDiv' => $this->getRoleDiv(),
            'roleDesc' => $this->getRoleDesc()
        ];
    }
}
