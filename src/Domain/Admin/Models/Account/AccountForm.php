<?php

namespace Src\Domain\Admin\Models\Account;

use Src\Domain\FormModel;
use Src\Enums\ApprovalStatus;

/**
 * Class AccountForm
 * @package Src\Domain\Admin\Models\Account
 */
class AccountForm extends FormModel
{
    /**
     * @var string|null
     */
    protected $loginId;
    /**
     * @var string|null
     */
    protected $name;
    /**
     * @var string|null
     */
    protected $email;
    /**
     * @var string|null
     */
    protected $password;
    /**
     * @var string|null
     */
    protected $roleDiv;

    public function __construct(array $input = [])
    {
        $this->loginId = array_get_string($input, 'login_id');
        $this->name = array_get_string($input, 'name');
        $this->email = array_get_string($input, 'email');
        $this->password = array_get_string($input, 'password');
        $this->roleDiv = array_get_string($input, 'role_div');
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->loginId;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return string|null
     */
    public function getRoleDiv(): ?string
    {
        return $this->roleDiv;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'login_id' => $this->getLoginId(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'password' => bcrypt($this->getPassword()),
            'role_div' => $this->getRoleDiv(),
            'approval_status' => ApprovalStatus::APPROVED
        ];
    }

    /**
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'role_div' => $this->getRoleDiv()
        ];
    }
}
