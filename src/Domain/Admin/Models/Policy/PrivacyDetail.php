<?php

namespace Src\Domain\Admin\Models\Privacy;

use App\Eloquent\Privacy;

/**
 * Class PrivacyDetail
 * @package Src\Domain\Admin\Models\Privacy
 */
class PrivacyDetail
{
    /**
     * @var Privacy
     */
    private $privacy;

    /**
     * PrivacyDetail constructor.
     * @param Privacy $privacy
     */
    public function __construct(Privacy $privacy)
    {
        $this->privacy = $privacy;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->privacy->id;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->privacy->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->privacy->body;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return (bool)$this->privacy->is_public;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->privacy->created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->privacy->updated_at;
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'isPublic' => $this->getIsPublic(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt()
        ];
    }
}
