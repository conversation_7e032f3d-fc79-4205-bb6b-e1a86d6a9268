<?php

namespace Src\Domain\Admin\Models\Auth;

use Src\Domain\FormModel;

class LoginForm extends FormModel
{
    protected $login_id;
    protected $password;

    /**
     * @var array
     */
    protected $fields = [
        'login_id' => 'string',
        'password' => 'string'
    ];

    /**
     * LoginForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $input = $this->castFields($input);
        $this->login_id = $input['login_id'];
        $this->password = $input['password'];
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }
}
