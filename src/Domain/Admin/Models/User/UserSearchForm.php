<?php

namespace Src\Domain\Admin\Models\User;

use Illuminate\Database\Eloquent\Builder;
use Src\Domain\FormModel;
use Src\Utils\Util;

/**
 * Class UserSearchForm
 *
 * This class represents a form for searching users with various criteria.
 *
 * @package Src\Domain\Admin\Models\User
 */
class UserSearchForm extends FormModel
{
    // Search filter properties
    protected ?string $prefecture = null;
    protected ?string $user_status = null;
    protected ?string $gender = null;
    protected ?string $keyword = null;

    /**
     * UserSearchForm constructor.
     *
     * @param array $input The input data for initializing the form
     */
    public function __construct(array $input = [])
    {
        // Map input fields to properties
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->user_status = array_get_string($input, 'user_status');
        $this->gender = array_get_string($input, 'gender');
        $this->keyword = array_get_string($input, 'keyword');
    }

    // Search filter getters
    public function getPrefecture(): ?string { return $this->prefecture; }
    public function getUserStatus(): ?string { return $this->user_status; }
    public function getGender(): ?string { return $this->gender; }
    public function getKeyword(): ?string { return $this->keyword; }

    /**
     * Add search conditions to the query based on the form input.
     *
     * @param Builder $query The query builder instance
     * @return Builder The modified query builder with search conditions applied
     */
    public function searchConditions(Builder $query): Builder
    {
        // Define search conditions as field-operator-value arrays
        $conditions = [
            ['prefecture', '=', $this->getPrefecture()],
            ['user_status', '=', $this->getUserStatus()],
            ['gender', '=', $this->getGender()],
        ];

        // Apply conditions to the query
        return Util::addSearchCondition($query, $conditions);
    }

    /**
     * Check if any search filters are applied
     *
     * @return bool True if at least one search filter is applied
     */
    public function hasFilters(): bool
    {
        return !empty($this->getPrefecture()) ||
               !empty($this->getUserStatus()) ||
               !empty($this->getGender()) ||
               !empty($this->getKeyword());
    }
}
