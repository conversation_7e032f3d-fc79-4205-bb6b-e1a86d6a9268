<?php

namespace Src\Domain\Admin\Models\Job;

use Illuminate\Database\Eloquent\Builder;
use Src\Utils\Util;

/**
 * Class JobSearchForm
 * @package Src\Domain\Admin\Models\Job
 */
class JobSearchForm
{
    private ?string $title;
    private ?string $prefecture;
    protected ?int $time_start;
    protected ?int $time_end;

    /**
     * JobSearchForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->title = array_get_string($input, 'title');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->time_start = array_get_int($input, 'time_start');
        $this->time_end = array_get_int($input, 'time_end');
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @return string|null
     */
    public function getPrefecture(): ?string
    {
        return $this->prefecture;
    }

    /**
     * @return int|null
     */
    public function getTimeStart(): ?int
    {
        return $this->time_start;
    }

    /**
     * @return int|null
     */
    public function getTimeEnd(): ?int
    {
        return $this->time_end;
    }

    public function searchConditions(Builder $query)
    {
        $conditions = [
            ['title', 'like', '%'.$this->getTitle().'%'],
            ['prefecture', '=', $this->getPrefecture()],
            ['time_start', '>=', $this->getTimeStart()],
            ['time_end', '<=', $this->getTimeEnd()],
        ];
        return Util::addSearchCondition($query, $conditions);
    }
}
