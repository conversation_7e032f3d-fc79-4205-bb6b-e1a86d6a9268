<?php

namespace Src\Domain\Admin\Models\Job;

use Illuminate\Database\Eloquent\Builder;
use Src\Utils\Util;

/**
 * Class JobSearchForm
 * @package Src\Domain\Admin\Models\Job
 */
class JobSearchForm
{
    private ?string $title;
    private ?int $category_id;
    private ?string $prefecture;
    protected ?string $salary_type;
    protected ?int $salary_min;
    protected ?int $salary_max;

    /**
     * JobSearchForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->title = array_get_string($input, 'title');
        $this->category_id = array_get_int($input, 'category_id');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->salary_type = array_get_string($input, 'salary_type');
        $this->salary_min = array_get_int($input, 'salary_min');
        $this->salary_max = array_get_int($input, 'salary_max');
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @return int|null
     */
    public function getCategoryId(): ?int
    {
        return $this->category_id;
    }

    /**
     * @return string|null
     */
    public function getPrefecture(): ?string
    {
        return $this->prefecture;
    }

    /**
     * @return string|null
     */
    public function getSalaryType(): ?string
    {
        return $this->salary_type;
    }

    /**
     * @return int|null
     */
    public function getSalaryMin(): ?int
    {
        return $this->salary_min;
    }

    /**
     * @return int|null
     */
    public function getSalaryMax(): ?int
    {
        return $this->salary_max;
    }

    public function searchConditions(Builder $query)
    {
        $conditions = [
            ['title', 'like', '%'.$this->getTitle().'%'],
            ['category_id', '=', $this->getCategoryId()],
            ['prefecture', '=', $this->getPrefecture()],
            ['salary_type', '=', $this->getSalaryType()],
            ['salary', '>=', $this->getSalaryMin()],
            ['salary', '<=', $this->getSalaryMax()],
        ];
        return Util::addSearchCondition($query, $conditions);
    }
}
