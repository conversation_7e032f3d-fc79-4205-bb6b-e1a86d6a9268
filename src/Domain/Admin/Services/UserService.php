<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Admin\Models\User\UserDetail;
use Src\Domain\Admin\Models\User\UserForm;
use Src\Domain\Admin\Models\User\UserSearchForm;
use Src\Enums\ApprovalStatus;
use Src\Enums\UserBankType;
use Src\Traits\UserTrait;
use Throwable;

/**
 * Class UserService
 * @package Src\Domain\Admin\Services
 */
class UserService
{
    use UserTrait;

    /**
     * Fetch All Users with pagination
     *
     * @param UserSearchForm $searchForm Search form with search criteria
     * @return array
     */
    public function fetchAll(UserSearchForm $searchForm): array
    {
        $query = $this->userQuery()
            ->orderBy('id', 'desc');

        // Apply keyword search if provided
        if ($keyword = $searchForm->getKeyword()) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%");
            });
        }

        // Apply additional search conditions
        $query = $searchForm->searchConditions($query);

        // Get paginated results
        $paginator = $query->paginate(PER_PAGE);

        // Transform user models to component-ready format
        $paginator->getCollection()->transform(function (User $user) {
            return (new UserDetail($user))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find user by ID or fail
     *
     * @param int $userId
     * @return array
     */
    public function findOrFail(int $userId): array
    {
        $user = $this->userQuery()->findOrFail($userId);
        return (new UserDetail($user))->toComponent();
    }

    /**
     * Store a new user
     *
     * @param UserForm $form
     * @return bool
     */
    public function store(UserForm $form): bool
    {
        try {
            return DB::transaction(function () use ($form) {
                // Generate unique code for user
                $codeExists = $this->userQuery()->pluck('code')->toArray();
                $code = gen_code($codeExists);

                // Prepare file attributes
                $uploadedFiles = [];
                $fileAttributes = [
                    'avatar_id' => $form->getAvatar(),
                    'health_certificate_id' => $form->getHealthCertificate(),
                    'passport_image_id' => $form->getPassportImage(),
                ];

                // Prepare and create user
                $userAttributes = $this->prepareUserAttributes(
                    $form->createUserAttributes($code),
                    $fileAttributes,
                    $uploadedFiles
                );

                /** @var User $user */
                $user = $this->userQuery()->createOrThrow($userAttributes);

                // Create bank information if applicable
                if ($form->getBankType() === UserBankType::BANK) {
                    $atmImageId = $this->createStorageFile($form->getAtmImage(), "$code/info");
                    $user->userBank()->create($form->createUserBankAttributes($atmImageId));
                }

                // Create residence card information
                $residenceFiles = [
                    'front_card_id' => $form->getFrontCard(),
                    'back_card_id' => $form->getBackCard(),
                    'identification_id' => $form->getIdentification(),
                ];

                $residenceCardAttributes = $this->prepareResidenceCardAttributes(
                    $form->createResidenceAttributes(),
                    $code,
                    $residenceFiles
                );

                $user->residenceCard()->create($residenceCardAttributes);
                $user->tmpResidenceCards()->create(array_merge($residenceCardAttributes, [
                    'approval_status' => ApprovalStatus::APPROVED,
                ]));

                logger_info('User created successfully', ['id' => $user->id, 'code' => $code]);
                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to store user: ', ['form_data' => $form->toArray()], $e);
            return false;
        }
    }

    /**
     * Delete a user
     *
     * @param int $userId
     * @return bool
     */
    public function delete(int $userId): bool
    {
        try {
            return DB::transaction(function () use ($userId) {
                $user = $this->userQuery()->findOrFail($userId);
                $user->delete();

                logger_info('User deleted successfully', ['id' => $userId]);
                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to delete user: ', ['user_id' => $userId], $e);
            return false;
        }
    }

    /**
     * Update user's base information
     *
     * @param int $userId
     * @param UserForm $form Base form with user information
     * @return bool
     */
    public function updateBase(int $userId, UserForm $form): bool
    {
        try {
            return DB::transaction(function () use ($userId, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($userId);
                $user->updateOrThrow($form->updateUserBaseAttributes());

                logger_info('User base info updated successfully', ['id' => $user->id]);
                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update user base info: ', ['user_id' => $userId, 'form_data' => $form->toArray()], $e);
            return false;
        }
    }

    /**
     * Update user's bank information
     *
     * @param int $userId
     * @param UserForm $form Bank form with bank information
     * @return bool
     */
    public function updateBank(int $userId, UserForm $form): bool
    {
        try {
            return DB::transaction(function () use ($userId, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($userId);

                // Update bank type
                $user->updateOrThrow([
                    'bank_type' => $form->getBankType(),
                ]);

                // Update or create bank information if applicable
                if ($form->getBankType() === UserBankType::BANK) {
                    $atmImageId = $this->createStorageFile($form->getAtmImage(), "$user->code/info");
                    $bankAttributes = $form->updateUserBankAttributes($atmImageId);

                    if ($user->userBank) {
                        $user->userBank()->update($bankAttributes);
                    } else {
                        $user->userBank()->create($bankAttributes);
                    }
                }

                logger_info('User bank info updated successfully', ['id' => $user->id]);
                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update user bank info: ', ['user_id' => $userId, 'form_data' => $form->toArray()], $e);
            return false;
        }
    }

    /**
     * Get user query builder
     *
     * @return Builder|User
     */
    private function userQuery(): Builder|User
    {
        return User::queryModel();
    }
}
