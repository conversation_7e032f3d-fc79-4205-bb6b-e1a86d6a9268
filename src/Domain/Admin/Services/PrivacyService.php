<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Privacy;
use Src\Domain\Admin\Models\Privacy\PrivacyDetail;
use Src\Domain\Admin\Models\Privacy\PrivacyForm;
use Src\Domain\Admin\Models\Privacy\PrivacySearchForm;
use DB;

class PrivacyService
{
    /**
     * Fetch all Privacy policies
     *
     * @param PrivacySearchForm $form
     */
    public function fetchAll(PrivacySearchForm $form)
    {
        $query = $this->privacyQuery()->orderBy('id', 'desc');

        if ($form->getTitle()) {
            $query->where('title', 'like', '%' . $form->getTitle() . '%');
        }

        if ($form->getIsPublic() !== null) {
            $query->where('is_public', $form->getIsPublic());
        }

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($privacy) {
            return (new PrivacyDetail($privacy))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find Privacy by id
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        /** @var Privacy $privacy */
        $privacy = $this->privacyQuery()->findOrFail($id);

        return (new PrivacyDetail($privacy))->toComponent();
    }

    /**
     * Store new Privacy
     *
     * @param PrivacyForm $form
     * @return bool
     */
    public function store(PrivacyForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Privacy $privacy */
                $privacy = $this->privacyQuery()->createOrThrow($form->createAttributes());
                logger_info('Successfully stored Privacy', ['id' => $privacy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger_error('Failed to store Privacy: ', $form->toArray(), $e);
        }
        return $result;
    }

    /**
     * Update Privacy
     *
     * @param int $id
     * @param PrivacyForm $form
     * @return bool
     */
    public function update(int $id, PrivacyForm $form): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id, $form) {
                /** @var Privacy $privacy */
                $privacy = $this->privacyQuery()->findOrFail($id);
                $privacy->updateOrThrow($form->editAttributes());
                logger()->info('Successfully updated Privacy', ['id' => $privacy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('Failed to update Privacy', $form->toArray());
        }
        return $result;
    }

    /**
     * Delete Privacy
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id) {
                /** @var Privacy $privacy */
                $privacy = $this->privacyQuery()->findOrFail($id);
                $privacy->delete();
                logger()->info('Successfully deleted Privacy', ['id' => $privacy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('Failed to delete Privacy', ['id' => $id]);
        }
        return $result;
    }

    private function privacyQuery()
    {
        return Privacy::query()->getModel();
    }
}
