<?php

namespace Src\Domain\Admin\Requests\Auth;

use Illuminate\Validation\Rule;
use Src\Domain\Admin\Models\Auth\RegisterForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class RegisterRequest
 * @package Src\Domain\Admin\Requests\Auth
 */
class RegisterRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'login_id' => [
                'required',
                'max:20',
                Rule::unique('accounts', 'login_id')
            ],
            'name' => [
                'required',
                'max:100'
            ],
            'email' => [
                'required',
                'email',
                'max:100',
                Rule::unique('accounts', 'email')
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'max:255',
            ],
            'password_confirmation' => [
                'required',
                'same:password'
            ]
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'login_id' => __('models/account.field.loginId'),
            'name' => __('models/account.field.name'),
            'email' => __('models/account.field.email'),
            'password' => __('models/account.field.password'),
            'password_confirmation' => __('models/account.field.passwordConfirmation'),
        ];
    }

    /**
     * @return RegisterForm
     */
    public function validatedForm(): RegisterForm
    {
        return new RegisterForm($this->validated());
    }
}
