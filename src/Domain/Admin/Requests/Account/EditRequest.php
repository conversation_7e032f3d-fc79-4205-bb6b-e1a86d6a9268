<?php

namespace Src\Domain\Admin\Requests\Account;

use Illuminate\Validation\Rule;
use Src\Domain\Admin\Models\Account\AccountForm;
use Src\Domain\Admin\Requests\FormRequest;
use Src\Enums\AccountRole;

/**
 * Class CreateRequest
 * @package Src\Domain\Admin\Requests\Account
 */
class EditRequest extends FormRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
            ],
            'email' => [
                'required',
                'email',
                Rule::unique('t_accounts', 'email')->ignore($this->route('account_id'))
            ],
            'role_div' => [
                'required',
                Rule::in(AccountRole::asArray())
            ]
        ];
    }


    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'name' => __('models/account.field.name'),
            'email' => __('models/account.field.email'),
            'role_div' => __('models/account.field.role'),
        ];
    }

    /**
     * @return AccountForm
     */
    public function validatedForm(): AccountForm
    {
        return new AccountForm($this->validated());
    }
}
