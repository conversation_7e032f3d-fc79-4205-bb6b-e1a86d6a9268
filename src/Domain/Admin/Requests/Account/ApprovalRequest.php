<?php

namespace Src\Domain\Admin\Requests\Account;

use Illuminate\Validation\Rule;
use Src\Domain\Admin\Models\Account\ApprovalForm;
use Src\Domain\Admin\Requests\FormRequest;
use Src\Enums\ApprovalStatus;

class ApprovalRequest extends FormRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'approval_status' => [
                'required',
                Rule::in(ApprovalStatus::asArray())
            ]
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'approval_status' => __('models/account.field.approvalStatus'),
        ];
    }

    /**
     * @return ApprovalForm
     */
    public function validatedForm(): ApprovalForm
    {
        return new ApprovalForm($this->validated());
    }
}
