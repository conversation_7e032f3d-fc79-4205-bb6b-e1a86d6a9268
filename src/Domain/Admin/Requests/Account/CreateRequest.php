<?php

namespace Src\Domain\Admin\Requests\Account;

use Src\Domain\Admin\Models\Account\AccountForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class CreateRequest
 * @package Src\Domain\Admin\Requests\Account
 */
class CreateRequest extends FormRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'login_id' => [
                'required',
                'unique:t_accounts,login_id'
            ],
            'name' => [
                'required',
            ],
            'email' => [
                'required',
                'email',
                'unique:t_accounts,login_id'
            ],
            'password' => [
                'required'
            ],
            'password_confirmation' => [
                'required',
                'same:password'
            ],
            'role_div' => 'required'
        ];
    }


    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'login_id' => __('models/account.field.loginId'),
            'name' => __('models/account.field.name'),
            'email' => __('models/account.field.email'),
            'password' => __('models/account.field.password'),
            'password_confirmation' => __('models/account.field.passwordConfirmation'),
            'role_div' => __('models/account.field.role'),
        ];
    }

    /**
     * @return AccountForm
     */
    public function validatedForm(): AccountForm
    {
        return new AccountForm($this->validated());
    }
}
