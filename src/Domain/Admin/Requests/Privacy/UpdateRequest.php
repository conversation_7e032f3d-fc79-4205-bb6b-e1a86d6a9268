<?php

namespace Src\Domain\Admin\Requests\Privacy;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Privacy\PrivacyForm;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'max:255'
            ],
            'body' => [
                'required',
                'string'
            ],
            'is_public' => [
                'boolean'
            ]
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'title' => __('models/privacy.field.title'),
            'body' => __('models/privacy.field.body'),
            'is_public' => __('models/privacy.field.isPublic'),
        ];
    }

    /**
     * Get the validated form data.
     */
    public function validatedForm(): PrivacyForm
    {
        return new PrivacyForm($this->validated());
    }
}
