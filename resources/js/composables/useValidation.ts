import { useI18n } from '@/composables/useI18n';
import {
  between,
  confirmed,
  digits,
  email,
  integer,
  length,
  max,
  max_value,
  min,
  min_value,
  not_one_of,
  numeric,
  one_of,
  regex,
  required,
} from '@vee-validate/rules';
import { useForm as useInertiaForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import { useUnsavedChanges } from '@/composables/useUnsavedChanges';

export type ValidationRule = {
  rule: string;
  message?: string;
};

export type ValidationRules = Record<string, (string | ValidationRule)[]>;

export type ValidationLabels = Record<string, string>;

export type UseValidationOptions<T> = {
  initialValues: T;
  labels?: ValidationLabels;
  enableUnsavedChanges?: boolean;
  unsavedChangesOptions?: {
    confirmMessage?: string;
    excludeFields?: string[];
  };
};

export function useValidation<T extends Record<string, any>>(initialValues: T): ReturnType<typeof createValidation<T>>;
export function useValidation<T extends Record<string, any>>(
  options: UseValidationOptions<T>,
): ReturnType<typeof createValidation<T>>;
export function useValidation<T extends Record<string, any>>(
  initialValuesOrOptions: T | UseValidationOptions<T>,
): ReturnType<typeof createValidation<T>> {
  const isOptionsObject =
    initialValuesOrOptions && typeof initialValuesOrOptions === 'object' && 'initialValues' in initialValuesOrOptions;

  if (isOptionsObject) {
    const options = initialValuesOrOptions as UseValidationOptions<T>;
    return createValidation(
      options.initialValues,
      options.labels,
      options.enableUnsavedChanges,
      options.unsavedChangesOptions,
    );
  } else {
    return createValidation(initialValuesOrOptions as T);
  }
}

function createValidation<T extends Record<string, any>>(
  initialValues: T,
  labels?: ValidationLabels,
  enableUnsavedChanges = true,
  unsavedChangesOptions?: {
    confirmMessage?: string;
    excludeFields?: string[];
  },
) {
  const { t } = useI18n();
  const form = useInertiaForm<T>(initialValues);
  const isPreview = ref(false);

  const unsavedChanges = enableUnsavedChanges ? useUnsavedChanges(form, unsavedChangesOptions) : null;

  if (unsavedChanges) {
    watch(
      () => form.data(),
      () => {
        unsavedChanges.updateDirtyState();
      },
      { deep: true },
    );
  }

  const parseRule = (ruleStr: string) => {
    const parts = ruleStr.split(':');
    const name = parts[0];
    const params = parts.length > 1 ? parts[1].split(',') : [];
    return { name, params };
  };

  const processRule = (rule: string | ValidationRule) => {
    if (typeof rule === 'string') {
      return { ruleStr: rule, customMessage: undefined };
    } else {
      return { ruleStr: rule.rule, customMessage: rule.message };
    }
  };

  const validateField = (field: string, value: any, rules: (string | ValidationRule)[]) => {
    form.clearErrors(field);

    const getFieldLabel = (fieldName: string) => {
      if (labels && labels[fieldName]) {
        return labels[fieldName];
      }

      return fieldName;
    };

    const fieldLabel = getFieldLabel(field);

    for (const rule of rules) {
      const { ruleStr, customMessage } = processRule(rule);
      const { name, params } = parseRule(ruleStr);
      let isValid: boolean | string | Promise<boolean | string> = true;
      let errorMessage = '';

      if (name === 'required') {
        isValid = required(value);
        errorMessage = t('validation.required', { attribute: fieldLabel });
      } else if (name === 'email') {
        isValid = email(value);
        errorMessage = t('validation.email', { attribute: fieldLabel });
      } else if (name === 'same') {
        const otherField = params[0];
        isValid = value === form.data()[otherField];
        errorMessage = t('validation.same', {
          attribute: fieldLabel,
          other: getFieldLabel(otherField),
        });
      } else if (name === 'min') {
        const minLength = String(params[0]);
        isValid = min(value, [minLength]);
        errorMessage = t('validation.min.string', {
          attribute: fieldLabel,
          min: minLength,
        });
      } else if (name === 'max') {
        const maxLength = String(params[0]);
        isValid = max(value, [maxLength]);
        errorMessage = t('validation.max.string', {
          attribute: fieldLabel,
          max: maxLength,
        });
      } else if (name === 'between') {
        const minValue = String(params[0]);
        const maxValue = String(params[1]);
        isValid = between(value, [minValue, maxValue]);
        errorMessage = t('validation.between.string', {
          attribute: fieldLabel,
          min: minValue,
          max: maxValue,
        });
      } else if (name === 'numeric') {
        isValid = numeric(value);
        errorMessage = t('validation.numeric', { attribute: fieldLabel });
      } else if (name === 'integer') {
        isValid = integer(value);
        errorMessage = t('validation.integer', { attribute: fieldLabel });
      } else if (name === 'confirmed') {
        const confirmField = `${field}_confirmation`;
        isValid = confirmed(value, form.data()[confirmField]);
        errorMessage = t('validation.confirmed', { attribute: fieldLabel });
      } else if (name === 'digits') {
        const digitsCount = String(params[0]);
        isValid = digits(value, [digitsCount]);
        errorMessage = t('validation.digits', {
          attribute: fieldLabel,
          digits: digitsCount,
        });
      } else if (name === 'regex') {
        const pattern = params[0];
        isValid = regex(value, { regex: pattern });
        errorMessage = t('validation.regex', { attribute: fieldLabel });
      } else if (name === 'in') {
        isValid = one_of(value, params);
        errorMessage = t('validation.in', { attribute: fieldLabel });
      } else if (name === 'not_in') {
        isValid = not_one_of(value, params);
        errorMessage = t('validation.not_in', { attribute: fieldLabel });
      } else if (name === 'min_value') {
        const minVal = String(params[0]);
        isValid = min_value(value, [minVal]);
        errorMessage = t('validation.min.numeric', {
          attribute: fieldLabel,
          min: minVal,
        });
      } else if (name === 'max_value') {
        const maxVal = String(params[0]);
        isValid = max_value(value, [maxVal]);
        errorMessage = t('validation.max.numeric', {
          attribute: fieldLabel,
          max: maxVal,
        });
      } else if (name === 'length') {
        const exactLength = String(params[0]);
        isValid = length(value, [exactLength]);
        errorMessage = t('validation.size.string', {
          attribute: fieldLabel,
          size: exactLength,
        });
      }

      if (!isValid) {
        const finalErrorMessage = customMessage || errorMessage;
        form.setError(field, finalErrorMessage);
        return false;
      }
    }

    return true;
  };

  const validateForm = (validationRules: ValidationRules) => {
    form.clearErrors();

    let isValid = true;
    const formData = form.data();

    for (const [field, rules] of Object.entries(validationRules)) {
      const isFieldValid = validateField(field, formData[field], rules);
      if (!isFieldValid) {
        isValid = false;
      }
    }

    return isValid;
  };

  const validateBeforePreview = (validationRules: ValidationRules) => {
    const isValid = validateForm(validationRules);

    if (isValid) {
      isPreview.value = true;
    }

    return isValid;
  };

  const setPreview = (value: boolean) => {
    isPreview.value = value;
  };

  return {
    form,
    isPreview,
    validateField,
    validateForm,
    validateBeforePreview,
    setPreview,
    ...(unsavedChanges
      ? {
          isDirty: unsavedChanges.isDirty,
          showConfirmModal: unsavedChanges.showConfirmModal,
          markAsClean: unsavedChanges.markAsClean,
          markAsDirty: unsavedChanges.markAsDirty,
          confirmLeave: unsavedChanges.confirmLeave,
          cancelLeave: unsavedChanges.cancelLeave,
          updateDirtyState: unsavedChanges.updateDirtyState,
          saveInitialState: unsavedChanges.saveInitialState,
        }
      : {}),
  };
}
