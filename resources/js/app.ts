import '../css/app.css';
import './bootstrap';
import { createInertiaApp } from '@inertiajs/vue3';
import { createApp, h } from 'vue';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { ZiggyVue } from 'ziggy-js';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from "vue";
import Toast, { POSITION } from 'vue-toastification';
import 'vue-toastification/dist/index.css';
import App from './App.vue';

const appName = window.document.getElementsByTagName('title')[0]?.innerText || 'Recruit';

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: async (name) => {
    const page = await resolvePageComponent(
      `./pages/${name}.vue`,
      import.meta.glob<DefineComponent>("./pages/**/*.vue")
    );
    page.default.layout = page.default.layout || App;
    return page;
  },
  setup({ el, App: InertiaApp, props, plugin }) {
    createApp({ render: () => h(InertiaApp, props) })
      .use(plugin)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .use(ZiggyVue, (window as any).Ziggy)
      .use(Toast, {
        position: POSITION.TOP_RIGHT,
        timeout: 2000,
        closeOnClick: true,
        icon: false
      })
      .component('App', App)
      .mount(el);
  },
  progress: {
    color: '#4B5563'
  }
});
