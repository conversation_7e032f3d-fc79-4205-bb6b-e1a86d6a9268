import { Paginator } from "@/types/paginator.ts";

export type ListPrivacyProps = {
    privacies: {
        data: PrivacyType[];
        paginator: Paginator
    };
}

export type DetailPrivacyProps = {
    privacy: PrivacyType;
}

export type PrivacyType = {
    id: number;
    title: string;
    body: string;
    isPublic: boolean;
    createdAt: string;
    updatedAt: string;
}

export type PrivacyFormType = {
    title: string;
    body: string;
    is_public: boolean;
}
