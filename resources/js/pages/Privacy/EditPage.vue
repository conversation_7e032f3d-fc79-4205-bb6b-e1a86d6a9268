<script setup lang="ts">
import { DetailPrivacyProps, PrivacyFormType } from "@/types/privacy.ts";
import { useI18n } from "@/composables/useI18n.ts";
import { ref } from "vue";
import { Head, InertiaForm, useForm } from "@inertiajs/vue3";
import { route } from "ziggy-js";
import EditForm from "@/pages/Privacy/common/EditForm.vue";
import Modal from "@/components/common/Modal.vue";

const props = defineProps<DetailPrivacyProps>();
const { t } = useI18n();
const isPreviewEdit = ref(false);

const formData = useForm<PrivacyFormType>({
  title: props.privacy.title,
  body: props.privacy.body,
  is_public: props.privacy.isPublic
});
const update = (form: InertiaForm<PrivacyFormType>) => {
  form.put(route('admin.privacy.update', props.privacy.id), {
    onSuccess: () => {
      isPreviewEdit.value = false;
    }
  });
};
</script>

<template>
  <Head :title="t('models/privacy.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/privacy.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <EditForm
      :form-data="formData"
      @openPreview="isPreviewEdit = true"
    />
  </div>
  <Modal v-if="isPreviewEdit" @close="isPreviewEdit = false" :title="t('models/privacy.screenName.edit')">
    <template #body>
      <EditForm
        :form-data="formData"
        @submit="update"
        @openPreview="isPreviewEdit = true"
        @close="isPreviewEdit = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
