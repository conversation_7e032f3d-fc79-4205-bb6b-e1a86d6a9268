<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import VInput from '@/components/common/shared/VInput.vue';
import { route } from 'ziggy-js';
import VRadio from '@/components/common/shared/VRadio.vue';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/solid/index.js';
import VPagination from '@/components/common/shared/VPagination.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import Button from '@/components/common/shared/Button.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import { ListUserProps } from '@/types/user.ts';
import { useI18n } from '@/composables/useI18n.ts';

defineProps<ListUserProps>();

const { t } = useI18n();

const searchForm = useForm({
  keyword: '',
  prefecture: '',
  user_status: '',
  age_start: '',
  age_end: '',
  gender: '',
});

const statusOptions = [];
const genderOptions = window.genderOptionTextAll;

function submitSearch() {
  searchForm.get(route('admin.user.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.user.index'));
}

function deleteUser(userId: number) {
  if (confirm('Are you sure you want to delete this user?')) {
    router.delete(route('admin.user.delete', userId));
  }
}
</script>

<template>
  <Head title="User" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/user.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('common.field.search') }}
          </h4>
          <form @submit.prevent="submitSearch">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-24">
              <div>
                <v-input
                  label="Prefecture"
                  v-model="searchForm.prefecture"
                  placeholder=""
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <label class="mb-1.5 block text-sm font-medium text-gray-700">Age</label>
                <div class="grid grid-cols-2 gap-4">
                  <v-input
                    v-model="searchForm.age_start"
                    placeholder=""
                    class="w-full"
                    :required="false"
                  />
                  <v-input
                    v-model="searchForm.age_end"
                    placeholder=""
                    class="w-full"
                    :required="false"
                  />
                </div>
              </div>
              <div>
                <v-select
                  label="Status"
                  v-model="searchForm.user_status"
                  :options="statusOptions"
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-radio
                  label="Gender"
                  v-model="searchForm.gender"
                  :options="genderOptions"
                  class="w-full"
                />
              </div>
              <div>
                <v-input
                  label="Keyword"
                  v-model="searchForm.keyword"
                  placeholder="Enter name or email..."
                  class="w-full"
                  :required="false"
                />
              </div>
            </div>
            <div class="flex items-center justify-center gap-2 mt-6">
              <Button size="sm" variant="outline" type="button" @click="resetSearch">{{
                t('common.btn.reset')
              }}</Button>
              <Button size="sm" variant="primary" type="submit">{{
                t('common.btn.search')
              }}</Button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex items-center justify-end mb-6">
        <button-link size="sm" variant="primary" :href="route('admin.user.create')">{{
          t('common.btn.create')
        }}</button-link>
      </div>
      <div class="overflow-hidden rounded-xl border border-gray-200 bg-white">
        <div class="max-w-full">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr class="border-b border-gray-200">
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.id') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.name') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.phone_number') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.gender') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.prefecture') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.birthday') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.email') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('models/user.field.user_status') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                >
                  {{ t('common.field.action') }}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="user in users.data" :key="user.id" class="border-t border-gray-100">
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.id }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.name }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.phoneNumber }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.genderName }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.prefecture }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.birthday }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.email }}</span>
                </td>
                <td class="border-t">
                  <span class="px-3 py-4 sm:px-6">{{ user.userStatusName }}</span>
                </td>
                <td class="px-5 py-4 sm:px-6">
                  <div class="flex text-center">
                    <Link :href="route('admin.user.show', user.id)"
                      ><eye-icon class="size-5 mr-2"
                    /></Link>
                    <trash-icon
                      class="size-5 mr-2 text-red-600 cursor-pointer"
                      @click="deleteUser(user.id)"
                    />
                  </div>
                </td>
              </tr>
              <tr v-if="users.data.length === 0">
                <td class="px-6 py-4 border-t" colspan="9">{{ t('common.noResult') }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="mt-2" v-if="users.data.length > 0">
        <v-pagination :paginator="users.paginator" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
