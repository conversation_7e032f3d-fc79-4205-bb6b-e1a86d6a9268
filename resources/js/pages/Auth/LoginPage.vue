<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { onMounted, onUnmounted } from 'vue';
import VInput from '@/components/common/shared/VInput.vue';
import { Head } from '@inertiajs/vue3';
import LoadingButton from '@/components/common/shared/LoadingButton.vue';
import { useLanguage } from '@/composables/useLanguage';
import { useI18n } from '@/composables/useI18n';
import { route } from 'ziggy-js';

const { showLanguageDropdown, currentLanguage, currentLanguageIcon, changeLanguage, languages, isLoading } =
  useLanguage();
const { t } = useI18n();

const form = useForm({
  login_id: '',
  password: '',
});

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.language-dropdown')) {
    showLanguageDropdown.value = false;
  }
};

function submit() {
  form.post(route('admin.auth.login'));
}
</script>
<template>
  <Head :title="t('models/auth.login.title')" />
  <div class="flex items-center justify-center p-6 min-h-screen relative">
    <!-- Language Selector -->
    <div class="absolute top-4 right-4">
      <div class="relative language-dropdown">
        <button
          v-if="!isLoading"
          @click="showLanguageDropdown = !showLanguageDropdown"
          class="flex items-center space-x-2 px-2 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none"
        >
          <span><img class="w-5" :src="`/images/icon/${currentLanguageIcon}.png`" alt="" /></span>
          <span>{{ currentLanguage }}</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        <div v-else class="w-16 h-7 bg-gray-200 animate-pulse rounded-md"></div>

        <!-- Dropdown Menu -->
        <div v-if="showLanguageDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
          <button
            v-for="lang in languages"
            :key="lang.code"
            @click="changeLanguage(lang.code)"
            class="flex space-x-3 w-full text-left px-4 py-1 text-sm text-gray-700 hover:bg-gray-100"
          >
            <span><img class="w-5" :src="`/images/icon/${lang.icon}.png`" alt="" /></span>
            <span>{{ lang.name }}</span>
          </button>
        </div>
      </div>
    </div>

    <div class="w-full max-w-md">
      <form class="mt-8 bg-white rounded-lg shadow-xl overflow-hidden" @submit.prevent="submit">
        <div class="px-10 py-12">
          <h1 class="text-center text-3xl font-bold">{{ t('models/auth.login.title') }}</h1>
          <div class="mt-6 mx-auto w-24 border-b-2" />
          <v-input
            v-model="form.login_id"
            :error="form.errors.login_id"
            class="mt-10"
            :label="t('models/auth.login.login_id')"
            type="text"
            autofocus
            autocapitalize="off"
          />
          <v-input
            v-model="form.password"
            :error="form.errors.password"
            class="mt-6"
            :label="t('models/auth.login.password')"
            type="password"
          />
        </div>
        <div class="flex px-10 py-4">
          <loading-button
            :loading="form.processing"
            class="justify-center w-full px-4 py-3 text-sm font-medium text-white transition rounded-lg bg-brand-500 shadow-theme-xs hover:bg-brand-600"
            type="submit"
          >
            {{ t('models/auth.login.submit') }}
          </loading-button>
        </div>
      </form>
    </div>
  </div>
</template>
