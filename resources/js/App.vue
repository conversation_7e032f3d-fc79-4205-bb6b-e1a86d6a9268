<script setup lang="ts">
import { computed, watch } from 'vue';
import { useToast } from 'vue-toastification';
import SidebarProvider from '@/layouts/SidebarProvider.vue';
import DefaultLayout from '@/layouts/DefaultLayout.vue';

const toast = useToast();
const { flash, author } = defineProps({
  flash: Object,
  author: Object,
});

const isAuthenticated = computed(() => !!author);

watch(
  () => flash,
  (newFlash, oldFlash) => {
    toast.clear();
    if (newFlash?.error) {
      toast.error(newFlash.error);
    } else if (newFlash?.success && newFlash?.success !== oldFlash?.success) {
      toast.success(newFlash?.success);
    }
  },
  { immediate: true },
);
</script>

<template>
  <template v-if="!isAuthenticated">
    <slot />
  </template>
  <SidebarProvider v-else>
    <DefaultLayout :author="author">
      <slot />
    </DefaultLayout>
  </SidebarProvider>
</template>

<style scoped></style>
