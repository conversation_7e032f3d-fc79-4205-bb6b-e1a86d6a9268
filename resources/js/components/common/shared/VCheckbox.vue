<script setup>
import { v4 as uuid } from "uuid";

defineProps({
  modelValue: Boolean,
  label: String,
  error: String,
  name: String,
  id: {
    type: String,
    default() {
      return `checkbox-${uuid()}`;
    }
  }
});

defineEmits(['update:modelValue']);
</script>

<template>
  <div :class="$attrs.class">
    <label class="flex items-center select-none">
      <input
        :id="id"
        :name="name"
        type="checkbox"
        class="mr-2 form-checkbox rounded text-indigo-600 focus:ring-indigo-600"
        v-bind="{ ...$attrs, class: null }"
        @change="$emit('update:modelValue', $event.target.checked)"
        :checked="modelValue"
      >
      <span class="text-sm">{{ label }}</span>
    </label>
    <div v-if="error" class="form-error">{{ error }}</div>
  </div>
</template>

<style scoped>

</style>
