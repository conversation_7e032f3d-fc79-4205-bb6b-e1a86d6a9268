<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use App\Eloquent\Concerns\Job\HasRelationship;
use App\Eloquent\Concerns\Job\HasScope;
use Database\Factories\JobFactory;

/**
 * Class Employer
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $recruitment_type
 * @property string $employer_email
 * @property string $employer_name
 * @property string $employer_phone_number
 * @property int $category_id
 * @property string $type
 * @property boolean $is_public
 * @property boolean $is_instant
 * @property int $thumbnail_id
 * @property string $title
 * @property string $description
 * @property string $benefits
 * @property string|Carbon $time_start
 * @property string|Carbon $time_end
 * @property int $age
 * @property string $gender
 * @property int $quantity
 * @property string $certificate_level
 * @property string $prefecture
 * @property string $address
 * @property string $salary_type
 * @property int $salary
 * @property string $travel_fee_type
 * @property int $travel_fee
 * @property string|Carbon $job_start_at
 * @property string|Carbon $recruit_start_at
 * @property string|Carbon $recruit_expired_at
 * @property boolean $is_recommended
 * @property boolean $is_filled
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property JobCategory $category
 * @property StorageFile $thumbnail
 * @property JobImage[] $images
 * @property JobApplication[] $jobApplications
 */
class Job extends Model
{
    use SoftDeletes, HasFactory, HasRelationship, HasScope;

    protected $table = 't_jobs';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'recruitment_type',
        'employer_email',
        'employer_name',
        'employer_phone_number',
        'category_id',
        'type',
        'is_public',
        'is_instant',
        'thumbnail_id',
        'title',
        'description',
        'benefits',
        'time_start',
        'time_end',
        'age',
        'gender',
        'quantity',
        'certificate_level',
        'prefecture',
        'address',
        'salary_type',
        'salary',
        'travel_fee',
        'travel_fee_type',
        'job_start_at',
        'recruit_start_at',
        'recruit_expired_at',
        'is_recommended',
        'is_filled'
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return JobFactory::new();
    }
}
