<?php

namespace App\Eloquent;

use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserBank
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property string $bank_code
 * @property string $branch_code
 * @property string $account_name
 * @property string $account_number
 * @property string $deposit_type
 * @property int $atm_image_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserBank extends Model
{
    protected $table = 't_user_banks';
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'bank_code',
        'branch_code',
        'account_name',
        'account_number',
        'deposit_type',
        'atm_image_id'
    ];

    /**
     * Get the ATM image associated with the bank.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function atmImage(): BelongsTo
    {
        return $this->belongsTo(StorageFile::class, 'atm_image_id');
    }
}
