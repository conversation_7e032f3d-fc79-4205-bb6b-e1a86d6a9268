<?php

namespace Database\Factories;

use App\Eloquent\Job;
use App\Eloquent\JobCategory;
use App\Eloquent\StorageFile;
use Illuminate\Database\Eloquent\Factories\Factory;
use Src\Enums\FeeType;
use Src\Enums\RecruitmentType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Model>
 */
class JobFactory extends Factory
{
    protected $model = Job::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'recruitment_type' => RecruitmentType::ADMIN,
            'thumbnail_id' => StorageFile::query()->inRandomOrder()->first()?->id ?? StorageFile::factory(),
            'employer_email' => $this->faker->email(),
            'employer_name' => $this->faker->name(),
            'employer_phone_number' => $this->faker->phoneNumber(),
            'category_id' => JobCategory::query()->inRandomOrder()->first()?->id ?? JobCategory::factory(),
            'type' => 'FULL_TIME',
            'is_public' => $this->faker->randomElement([true, false]),
            'is_instant' => $this->faker->randomElement([true, false]),
            'title' => $this->faker->jobTitle(),
            'description' => $this->faker->text(),
            'benefits' => $this->faker->text(),
            'time_start' => $timeStart = $this->faker->time('H:i:s'),
            'time_end' => date('H:i:s', strtotime($timeStart . ' +1 hour')),
            'age' => $this->faker->numberBetween(18, 50),
            'gender' => $this->faker->randomElement(\Src\Enums\Gender::asArray()),
            'quantity' => $this->faker->numberBetween(),
            'certificate_level' => $this->faker->randomElement(\Src\Enums\CertificateLevel::asArray()),
            'prefecture' => $this->faker->city(),
            'address' => $this->faker->address(),
            'recruit_start_at' => $this->faker->dateTimeBetween('now', '+9 month')->format('Y-m-d'),
            'recruit_expired_at' => $this->faker->dateTimeBetween('now', '+9 month')->format('Y-m-d'),
            'salary_type' => FeeType::DAY,
            'salary' => $this->faker->numberBetween(1000, 3000),
            'travel_fee_type' => FeeType::DAY,
            'travel_fee' => $this->faker->numberBetween(100, 300),
        ];
    }
}
