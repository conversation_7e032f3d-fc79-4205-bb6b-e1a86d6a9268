<?php

namespace Database\Seeders;

use App\Eloquent\Account;
use App\Eloquent\JobCategory;
use App\Eloquent\Job;
use App\Eloquent\StorageFile;
use App\Eloquent\User;
use Illuminate\Database\Seeder;
use Src\Enums\AccountRole;
use Src\Enums\ApprovalStatus;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Account::create([
            'login_id' => 'admin',
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456'),
            'role_div' => AccountRole::SUPER,
            'approval_status' => ApprovalStatus::APPROVED
        ]);

        $this->call([
            PrefectureSeeder::class,
            StationSeeder::class,
            BankNameSeeder::class
        ]);

        if (!app()->environment('production')) {
            $this->call([
                DevelopSeeder::class
            ]);
        }
    }
}
